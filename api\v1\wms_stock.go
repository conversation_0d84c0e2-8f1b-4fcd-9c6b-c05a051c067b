package v1

import "github.com/lib/pq"

type WmsStockCreateParams struct {
	SkuId     uint          `json:"skuId" binding:"required" example:"1"`
	AreaPath  pq.Int64Array `json:"areaPath" swaggertype:"array,integer" binding:"required" example:"1,2,3"`
	Num       uint          `json:"num" binding:"required" example:"100"`
	TenantId  uint          `json:"tenantId" example:"1"`
	CreatedBy string        `json:"createdBy" example:"管理员"`
	UpdatedBy string        `json:"updatedBy" example:"管理员"`
}

type WmsStockUpdateParams struct {
	SkuId     uint          `json:"skuId" binding:"required" example:"1"`
	AreaPath  pq.Int64Array `json:"areaPath" swaggertype:"array,integer" binding:"required" example:"1,2,3"`
	Num       uint          `json:"num" binding:"required" example:"100"`
	UpdatedBy string        `json:"updatedBy" example:"管理员"`
}

type WmsStockAdjustParams struct {
	Num       int    `json:"num" binding:"required" example:"10"`
	Reason    string `json:"reason" binding:"required,max=255" example:"盘点调整"`
	UpdatedBy string `json:"updatedBy" example:"管理员"`
}

type WmsStockResponse struct {
	ID        uint          `json:"id"`
	SkuId     uint          `json:"skuId"`
	AreaPath  pq.Int64Array `json:"areaPath"`
	Num       uint          `json:"num"`
	TenantId  uint          `json:"tenantId"`
	CreatedBy string        `json:"createdBy"`
	UpdatedBy string        `json:"updatedBy"`
	CreatedAt string        `json:"createdAt"`
	UpdatedAt string        `json:"updatedAt"`
}
