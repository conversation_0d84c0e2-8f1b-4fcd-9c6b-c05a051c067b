package v1

import "gorm.io/datatypes"

type WmsTransferCreateParams struct {
	Code      string `json:"code" binding:"required,max=64" example:"TRF202412250001"`
	Type      uint   `json:"type" example:"1"`
	Summary   string `json:"summary" example:"库位调拨"`
	Status    uint   `json:"status" example:"1"`
	TenantId  uint   `json:"tenantId" example:"1"`
	CreatedBy string `json:"createdBy" example:"管理员"`
	UpdatedBy string `json:"updatedBy" example:"管理员"`
}

type WmsTransferUpdateParams struct {
	Code      string `json:"code" binding:"required,max=64" example:"TRF202412250001"`
	Type      uint   `json:"type" example:"1"`
	Summary   string `json:"summary" example:"库位调拨"`
	Status    uint   `json:"status" example:"1"`
	UpdatedBy string `json:"updatedBy" example:"管理员"`
}

type WmsTransferResponse struct {
	ID         uint   `json:"id"`
	Code       string `json:"code"`
	Type       uint   `json:"type"`
	TypeName   string `json:"typeName"`
	Summary    string `json:"summary"`
	Status     uint   `json:"status"`
	StatusName string `json:"statusName"`
	TenantId   uint   `json:"tenantId"`
	CreatedBy  string `json:"createdBy"`
	UpdatedBy  string `json:"updatedBy"`
	CreatedAt  string `json:"createdAt"`
	UpdatedAt  string `json:"updatedAt"`
}

// 调拨单物料明细
type WmsTransferSkuCreateParams struct {
	TransferId  uint           `json:"transferId" binding:"required" example:"1"`
	SkuId       uint           `json:"skuId" binding:"required" example:"1"`
	Num         uint           `json:"num" binding:"required" example:"10"`
	FromAreaNum datatypes.JSON `json:"fromAreaNum" swaggertype:"object" example:"{\"1\":{\"2\":5,\"3\":5}}"`
	ToAreaNum   datatypes.JSON `json:"toAreaNum" swaggertype:"object" example:"{\"1\":{\"4\":10}}"`
	Status      bool           `json:"status" example:"false"`
	TenantId    uint           `json:"tenantId" example:"1"`
	CreatedBy   string         `json:"createdBy" example:"管理员"`
	UpdatedBy   string         `json:"updatedBy" example:"管理员"`
}

type WmsTransferSkuUpdateParams struct {
	TransferId  uint           `json:"transferId" binding:"required" example:"1"`
	SkuId       uint           `json:"skuId" binding:"required" example:"1"`
	Num         uint           `json:"num" binding:"required" example:"10"`
	FromAreaNum datatypes.JSON `json:"fromAreaNum" swaggertype:"object" example:"{\"1\":{\"2\":5,\"3\":5}}"`
	ToAreaNum   datatypes.JSON `json:"toAreaNum" swaggertype:"object" example:"{\"1\":{\"4\":10}}"`
	Status      bool           `json:"status" example:"false"`
	UpdatedBy   string         `json:"updatedBy" example:"管理员"`
}

type WmsTransferSkuResponse struct {
	ID          uint           `json:"id"`
	TransferId  uint           `json:"transferId"`
	SkuId       uint           `json:"skuId"`
	Num         uint           `json:"num"`
	FromAreaNum datatypes.JSON `json:"fromAreaNum"`
	ToAreaNum   datatypes.JSON `json:"toAreaNum"`
	Status      bool           `json:"status"`
	TenantId    uint           `json:"tenantId"`
	CreatedBy   string         `json:"createdBy"`
	UpdatedBy   string         `json:"updatedBy"`
	CreatedAt   string         `json:"createdAt"`
	UpdatedAt   string         `json:"updatedAt"`
}
