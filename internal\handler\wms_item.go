package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type WmsItemHandler struct {
	*Handler
	wmsItemService service.WmsItemService
}

func NewWmsItemHandler(
	handler *Handler,
	wmsItemService service.WmsItemService,
) *WmsItemHandler {
	return &WmsItemHandler{
		Handler:        handler,
		wmsItemService: wmsItemService,
	}
}

// Create godoc
// @Summary 创建物料
// @Schemes
// @Description 创建新的物料记录
// @Tags 仓储模块,物料管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.WmsItemCreateParams true "物料信息"
// @Success 200 {object} v1.Response
// @Router /wms/items [post]
func (h *WmsItemHandler) Create(ctx *gin.Context) {
	var req v1.WmsItemCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsItemService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新物料
// @Schemes
// @Description 更新指定ID的物料信息
// @Tags 仓储模块,物料管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "物料ID"
// @Param request body v1.WmsItemUpdateParams true "物料信息"
// @Success 200 {object} v1.Response
// @Router /wms/items/{id} [patch]
func (h *WmsItemHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.WmsItemUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsItemService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除物料
// @Schemes
// @Description 删除指定ID的物料
// @Tags 仓储模块,物料管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "物料ID"
// @Success 200 {object} v1.Response
// @Router /wms/items/{id} [delete]
func (h *WmsItemHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsItemService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除物料
// @Schemes
// @Description 批量删除指定IDs的物料
// @Tags 仓储模块,物料管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "物料IDs"
// @Success 200 {object} v1.Response
// @Router /wms/items [delete]
func (h *WmsItemHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsItemService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取物料
// @Schemes
// @Description 获取指定ID的物料信息
// @Tags 仓储模块,物料管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "物料ID"
// @Success 200 {object} v1.Response{data=v1.WmsItemResponse}
// @Router /wms/items/{id} [get]
func (h *WmsItemHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	item, err := h.wmsItemService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, item)
}

// List godoc
// @Summary 获取物料列表
// @Schemes
// @Description 分页获取物料列表
// @Tags 仓储模块,物料管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Param metaId query int false "分类ID筛选" example:"1"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.WmsItemResponse}}
// @Router /wms/items [get]
func (h *WmsItemHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 状态筛选
	if status := ctx.DefaultQuery("status", ""); status != "" {
		params.AddFilter("status", status)
	}

	// 名称筛选
	if name := ctx.DefaultQuery("name", ""); name != "" {
		params.AddFilter("name_like", name)
	}

	// 编号筛选
	if code := ctx.DefaultQuery("code", ""); code != "" {
		params.AddFilter("code_like", code)
	}

	// 分类ID筛选
	if metaId := ctx.DefaultQuery("metaId", ""); metaId != "" {
		params.AddFilter("meta_id", metaId)
	}

	result, err := h.wmsItemService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
