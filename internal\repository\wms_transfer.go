package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type WmsTransferRepository interface {
	Create(ctx context.Context, transfer *model.WmsTransfer) error
	Update(ctx context.Context, transfer *model.WmsTransfer) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.WmsTransfer, error)
	GetByIds(ctx context.Context, ids []uint) ([]*model.WmsTransfer, error)
	GetByCode(ctx context.Context, code string) (*model.WmsTransfer, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.WmsTransfer, int64, error)
}

func NewWmsTransferRepository(
	repository *Repository,
) WmsTransferRepository {
	return &wmsTransferRepository{
		Repository: repository,
	}
}

type wmsTransferRepository struct {
	*Repository
}

func (r *wmsTransferRepository) Create(ctx context.Context, transfer *model.WmsTransfer) error {
	if err := r.DB(ctx).Create(transfer).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsTransferRepository) Update(ctx context.Context, transfer *model.WmsTransfer) error {
	if err := r.DB(ctx).Save(transfer).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsTransferRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.WmsTransfer{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsTransferRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.WmsTransfer{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsTransferRepository) Get(ctx context.Context, id uint) (*model.WmsTransfer, error) {
	var transfer model.WmsTransfer
	if err := r.DB(ctx).First(&transfer, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &transfer, nil
}

func (r *wmsTransferRepository) GetByIds(ctx context.Context, ids []uint) ([]*model.WmsTransfer, error) {
	var transfers []*model.WmsTransfer
	if len(ids) == 0 {
		return transfers, nil
	}

	if err := r.DB(ctx).Where("id IN ?", ids).Find(&transfers).Error; err != nil {
		return nil, err
	}
	return transfers, nil
}

func (r *wmsTransferRepository) GetByCode(ctx context.Context, code string) (*model.WmsTransfer, error) {
	var transfer model.WmsTransfer
	if err := r.DB(ctx).Where("code = ?", code).First(&transfer).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &transfer, nil
}

func (r *wmsTransferRepository) List(ctx context.Context, params *pagination.Params) ([]*model.WmsTransfer, int64, error) {
	var records []*model.WmsTransfer
	var total int64

	db := r.DB(ctx).Model(&model.WmsTransfer{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.WmsTransfer{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
