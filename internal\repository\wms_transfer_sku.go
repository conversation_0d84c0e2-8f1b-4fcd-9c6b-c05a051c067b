package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type WmsTransferSkuRepository interface {
	Create(ctx context.Context, transferSku *model.WmsTransferSku) error
	Update(ctx context.Context, transferSku *model.WmsTransferSku) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.WmsTransferSku, error)
	GetByIds(ctx context.Context, ids []uint) ([]*model.WmsTransferSku, error)
	GetByTransferId(ctx context.Context, transferId uint) ([]*model.WmsTransferSku, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.WmsTransferSku, int64, error)
	BatchCreate(ctx context.Context, transferSkus []*model.WmsTransferSku) error
	DeleteByTransferId(ctx context.Context, transferId uint) error
}

func NewWmsTransferSkuRepository(
	repository *Repository,
) WmsTransferSkuRepository {
	return &wmsTransferSkuRepository{
		Repository: repository,
	}
}

type wmsTransferSkuRepository struct {
	*Repository
}

func (r *wmsTransferSkuRepository) Create(ctx context.Context, transferSku *model.WmsTransferSku) error {
	if err := r.DB(ctx).Create(transferSku).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsTransferSkuRepository) Update(ctx context.Context, transferSku *model.WmsTransferSku) error {
	if err := r.DB(ctx).Save(transferSku).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsTransferSkuRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.WmsTransferSku{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsTransferSkuRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.WmsTransferSku{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsTransferSkuRepository) Get(ctx context.Context, id uint) (*model.WmsTransferSku, error) {
	var transferSku model.WmsTransferSku
	if err := r.DB(ctx).First(&transferSku, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &transferSku, nil
}

func (r *wmsTransferSkuRepository) GetByIds(ctx context.Context, ids []uint) ([]*model.WmsTransferSku, error) {
	var transferSkus []*model.WmsTransferSku
	if len(ids) == 0 {
		return transferSkus, nil
	}

	if err := r.DB(ctx).Where("id IN ?", ids).Find(&transferSkus).Error; err != nil {
		return nil, err
	}
	return transferSkus, nil
}

func (r *wmsTransferSkuRepository) GetByTransferId(ctx context.Context, transferId uint) ([]*model.WmsTransferSku, error) {
	var transferSkus []*model.WmsTransferSku
	if err := r.DB(ctx).Where("transfer_id = ?", transferId).Find(&transferSkus).Error; err != nil {
		return nil, err
	}
	return transferSkus, nil
}

func (r *wmsTransferSkuRepository) List(ctx context.Context, params *pagination.Params) ([]*model.WmsTransferSku, int64, error) {
	var records []*model.WmsTransferSku
	var total int64

	db := r.DB(ctx).Model(&model.WmsTransferSku{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.WmsTransferSku{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

func (r *wmsTransferSkuRepository) BatchCreate(ctx context.Context, transferSkus []*model.WmsTransferSku) error {
	if len(transferSkus) == 0 {
		return nil
	}

	// 使用批量插入提高性能
	if err := r.DB(ctx).CreateInBatches(transferSkus, 100).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsTransferSkuRepository) DeleteByTransferId(ctx context.Context, transferId uint) error {
	return r.DB(ctx).Where("transfer_id = ?", transferId).Delete(&model.WmsTransferSku{}).Error
}
