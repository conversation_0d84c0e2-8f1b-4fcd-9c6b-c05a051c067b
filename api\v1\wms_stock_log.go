package v1

type WmsStockLogCreateParams struct {
	SkuId     uint   `json:"skuId" binding:"required" example:"1"`
	Type      uint   `json:"type" binding:"required" example:"1"`
	RelatedNo string `json:"relatedNo" binding:"required,max=64" example:"REC202412250001"`
	Summary   string `json:"summary" example:"入库操作"`
	TenantId  uint   `json:"tenantId" example:"1"`
	CreatedBy string `json:"createdBy" example:"管理员"`
	UpdatedBy string `json:"updatedBy" example:"管理员"`
}

type WmsStockLogUpdateParams struct {
	SkuId     uint   `json:"skuId" binding:"required" example:"1"`
	Type      uint   `json:"type" binding:"required" example:"1"`
	RelatedNo string `json:"relatedNo" binding:"required,max=64" example:"REC202412250001"`
	Summary   string `json:"summary" example:"入库操作"`
	UpdatedBy string `json:"updatedBy" example:"管理员"`
}

type WmsStockLogResponse struct {
	ID        uint   `json:"id"`
	SkuId     uint   `json:"skuId"`
	Type      uint   `json:"type"`
	TypeName  string `json:"typeName"`
	RelatedNo string `json:"relatedNo"`
	Summary   string `json:"summary"`
	TenantId  uint   `json:"tenantId"`
	CreatedBy string `json:"createdBy"`
	UpdatedBy string `json:"updatedBy"`
	CreatedAt string `json:"createdAt"`
	UpdatedAt string `json:"updatedAt"`
}

// 库存日志统计响应
type WmsStockLogSummaryResponse struct {
	Type     uint   `json:"type"`
	TypeName string `json:"typeName"`
	LogCount int    `json:"logCount"`
	SkuCount int    `json:"skuCount"`
}
