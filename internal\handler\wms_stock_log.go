package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type WmsStockLogHandler struct {
	*Handler
	wmsStockLogService service.WmsStockLogService
}

func NewWmsStockLogHandler(
	handler *Handler,
	wmsStockLogService service.WmsStockLogService,
) *WmsStockLogHandler {
	return &WmsStockLogHandler{
		Handler:            handler,
		wmsStockLogService: wmsStockLogService,
	}
}

// Create godoc
// @Summary 创建库存日志
// @Schemes
// @Description 创建新的库存日志记录
// @Tags 仓储模块,库存管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.WmsStockLogCreateParams true "库存日志信息"
// @Success 200 {object} v1.Response
// @Router /wms/stock_logs [post]
func (h *WmsStockLogHandler) Create(ctx *gin.Context) {
	var req v1.WmsStockLogCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsStockLogService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新库存日志
// @Schemes
// @Description 更新指定ID的库存日志信息
// @Tags 仓储模块,库存管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "库存日志ID"
// @Param request body v1.WmsStockLogUpdateParams true "库存日志信息"
// @Success 200 {object} v1.Response
// @Router /wms/stock_logs/{id} [patch]
func (h *WmsStockLogHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.WmsStockLogUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsStockLogService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除库存日志
// @Schemes
// @Description 删除指定ID的库存日志
// @Tags 仓储模块,库存管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "库存日志ID"
// @Success 200 {object} v1.Response
// @Router /wms/stock_logs/{id} [delete]
func (h *WmsStockLogHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsStockLogService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除库存日志
// @Schemes
// @Description 批量删除指定IDs的库存日志
// @Tags 仓储模块,库存管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "库存日志IDs"
// @Success 200 {object} v1.Response
// @Router /wms/stock_logs [delete]
func (h *WmsStockLogHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsStockLogService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取库存日志
// @Schemes
// @Description 获取指定ID的库存日志信息
// @Tags 仓储模块,库存管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "库存日志ID"
// @Success 200 {object} v1.Response{data=v1.WmsStockLogResponse}
// @Router /wms/stock_logs/{id} [get]
func (h *WmsStockLogHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	log, err := h.wmsStockLogService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, log)
}

// GetByRelatedNo godoc
// @Summary 根据关联单号获取库存日志
// @Schemes
// @Description 根据关联单号获取相关的库存日志列表
// @Tags 仓储模块,库存管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param relatedNo path string true "关联单号"
// @Success 200 {object} v1.Response{data=[]v1.WmsStockLogResponse}
// @Router /wms/stock_logs/related/{relatedNo} [get]
func (h *WmsStockLogHandler) GetByRelatedNo(ctx *gin.Context) {
	relatedNo := ctx.Param("relatedNo")
	if relatedNo == "" {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	logs, err := h.wmsStockLogService.GetByRelatedNo(ctx, relatedNo)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, logs)
}

// List godoc
// @Summary 获取库存日志列表
// @Schemes
// @Description 分页获取库存日志列表
// @Tags 仓储模块,库存管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param skuId query int false "物料规格ID筛选" example:"1"
// @Param type query int false "日志类型筛选" example:"1"
// @Param relatedNo query string false "关联单号筛选" example:"REC"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.WmsStockLogResponse}}
// @Router /wms/stock_logs [get]
func (h *WmsStockLogHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 物料规格ID筛选
	if skuId := ctx.DefaultQuery("skuId", ""); skuId != "" {
		params.AddFilter("sku_id", skuId)
	}

	// 日志类型筛选
	if logType := ctx.DefaultQuery("type", ""); logType != "" {
		params.AddFilter("type", logType)
	}

	// 关联单号筛选
	if relatedNo := ctx.DefaultQuery("relatedNo", ""); relatedNo != "" {
		params.AddFilter("related_no_like", relatedNo)
	}

	// 描述筛选
	if summary := ctx.DefaultQuery("summary", ""); summary != "" {
		params.AddFilter("summary_like", summary)
	}

	result, err := h.wmsStockLogService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
