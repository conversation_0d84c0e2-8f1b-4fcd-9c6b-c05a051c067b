// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"daisy-server/internal/handler"
	"daisy-server/internal/job"
	"daisy-server/internal/repository"
	"daisy-server/internal/server"
	"daisy-server/internal/service"
	"daisy-server/pkg/app"
	"daisy-server/pkg/jwt"
	"daisy-server/pkg/log"
	"daisy-server/pkg/rbac"
	"daisy-server/pkg/server/http"
	"daisy-server/pkg/sid"
	"github.com/google/wire"
	"github.com/spf13/viper"
)

// Injectors from wire.go:

func NewWire(viperViper *viper.Viper, logger *log.Logger) (*app.App, func(), error) {
	jwtJWT := jwt.NewJwt(viperViper)
	db := repository.NewDB(viperViper, logger)
	repositoryRepository := repository.NewRepository(logger, db)
	client := repository.NewRedis(viperViper)
	rbacRBAC, err := rbac.NewRBAC(viperViper, repositoryRepository, logger, client)
	if err != nil {
		return nil, nil, err
	}
	sysLogRepository := repository.NewSysLogRepository(repositoryRepository)
	handlerHandler := handler.NewHandler(logger)
	transaction := repository.NewTransaction(repositoryRepository)
	sidSid := sid.NewSid()
	serviceService := service.NewService(transaction, logger, sidSid, jwtJWT)
	authService := service.NewAuthService(serviceService)
	sysMenuRepository := repository.NewSysMenuRepository(repositoryRepository)
	sysMenuService := service.NewSysMenuService(serviceService, sysMenuRepository)
	authHandler := handler.NewAuthHandler(handlerHandler, authService, sysMenuService)
	uploadService := service.NewUploadService(serviceService, viperViper)
	uploadHandler := handler.NewUploadHandler(handlerHandler, uploadService)
	sysConfigRepository := repository.NewSysConfigRepository(repositoryRepository)
	sysConfigService := service.NewSysConfigService(serviceService, sysConfigRepository)
	sysConfigHandler := handler.NewSysConfigHandler(handlerHandler, sysConfigService)
	sysDictRepository := repository.NewSysDictRepository(repositoryRepository)
	sysDictService := service.NewSysDictService(serviceService, sysDictRepository)
	sysDictHandler := handler.NewSysDictHandler(handlerHandler, sysDictService)
	sysMenuHandler := handler.NewSysMenuHandler(handlerHandler, sysMenuService)
	sysApiRepository := repository.NewSysApiRepository(repositoryRepository)
	sysApiService := service.NewSysApiService(serviceService, sysApiRepository)
	sysApiHandler := handler.NewSysApiHandler(handlerHandler, sysApiService, viperViper)
	sysRoleRepository := repository.NewSysRoleRepository(repositoryRepository)
	sysRoleService := service.NewSysRoleService(serviceService, sysRoleRepository)
	sysRoleHandler := handler.NewSysRoleHandler(handlerHandler, sysRoleService)
	sysUserRepository := repository.NewSysUserRepository(repositoryRepository)
	sysTenantRepository := repository.NewSysTenantRepository(repositoryRepository)
	sysUserService := service.NewSysUserService(serviceService, sysUserRepository, sysTenantRepository)
	sysUserHandler := handler.NewSysUserHandler(handlerHandler, sysUserService)
	sysTenantService := service.NewSysTenantService(serviceService, sysTenantRepository)
	sysTenantHandler := handler.NewSysTenantHandler(handlerHandler, sysTenantService)
	sysLogService := service.NewSysLogService(serviceService, sysLogRepository)
	sysLogHandler := handler.NewSysLogHandler(handlerHandler, sysLogService)
	cmsMetaRepository := repository.NewCmsMetaRepository(repositoryRepository)
	cmsMetaService := service.NewCmsMetaService(serviceService, cmsMetaRepository)
	cmsMetaHandler := handler.NewCmsMetaHandler(handlerHandler, cmsMetaService)
	cmsPostRepository := repository.NewCmsPostRepository(repositoryRepository)
	cmsPostService := service.NewCmsPostService(serviceService, cmsPostRepository)
	cmsPostHandler := handler.NewCmsPostHandler(handlerHandler, cmsPostService)
	wmsAreaRepository := repository.NewWmsAreaRepository(repositoryRepository)
	wmsAreaService := service.NewWmsAreaService(serviceService, wmsAreaRepository)
	wmsAreaHandler := handler.NewWmsAreaHandler(handlerHandler, wmsAreaService)
	wmsStaffService := service.NewWmsStaffService(serviceService, sysUserRepository, sysTenantRepository)
	wmsStaffHandler := handler.NewWmsStaffHandler(handlerHandler, wmsStaffService)
	wmsPartnerRepository := repository.NewWmsPartnerRepository(repositoryRepository)
	wmsPartnerService := service.NewWmsPartnerService(serviceService, wmsPartnerRepository)
	wmsPartnerHandler := handler.NewWmsPartnerHandler(handlerHandler, wmsPartnerService)
	wmsItemRepository := repository.NewWmsItemRepository(repositoryRepository)
	wmsItemService := service.NewWmsItemService(serviceService, wmsItemRepository)
	wmsItemHandler := handler.NewWmsItemHandler(handlerHandler, wmsItemService)
	wmsMetaRepository := repository.NewWmsMetaRepository(repositoryRepository)
	wmsMetaService := service.NewWmsMetaService(serviceService, wmsMetaRepository)
	wmsMetaHandler := handler.NewWmsMetaHandler(handlerHandler, wmsMetaService)
	wmsUnitRepository := repository.NewWmsUnitRepository(repositoryRepository)
	wmsUnitService := service.NewWmsUnitService(serviceService, wmsUnitRepository)
	wmsUnitHandler := handler.NewWmsUnitHandler(handlerHandler, wmsUnitService)
	wmsSkuRepository := repository.NewWmsSkuRepository(repositoryRepository)
	wmsSkuService := service.NewWmsSkuService(serviceService, wmsSkuRepository)
	wmsSkuHandler := handler.NewWmsSkuHandler(handlerHandler, wmsSkuService)
	wmsStockRepository := repository.NewWmsStockRepository(repositoryRepository)
	wmsStockService := service.NewWmsStockService(serviceService, wmsStockRepository)
	wmsStockHandler := handler.NewWmsStockHandler(handlerHandler, wmsStockService)
	wmsStockLogRepository := repository.NewWmsStockLogRepository(repositoryRepository)
	wmsStockLogService := service.NewWmsStockLogService(serviceService, wmsStockLogRepository)
	wmsStockLogHandler := handler.NewWmsStockLogHandler(handlerHandler, wmsStockLogService)
	wmsTransferRepository := repository.NewWmsTransferRepository(repositoryRepository)
	wmsTransferService := service.NewWmsTransferService(serviceService, wmsTransferRepository)
	wmsTransferHandler := handler.NewWmsTransferHandler(handlerHandler, wmsTransferService)
	wmsTransferSkuRepository := repository.NewWmsTransferSkuRepository(repositoryRepository)
	wmsTransferSkuService := service.NewWmsTransferSkuService(serviceService, wmsTransferSkuRepository)
	wmsTransferSkuHandler := handler.NewWmsTransferSkuHandler(handlerHandler, wmsTransferSkuService)
	httpServer := server.NewHTTPServer(logger, viperViper, jwtJWT, rbacRBAC, sysLogRepository, authHandler, uploadHandler, sysConfigHandler, sysDictHandler, sysMenuHandler, sysApiHandler, sysRoleHandler, sysUserHandler, sysTenantHandler, sysLogHandler, cmsMetaHandler, cmsPostHandler, wmsAreaHandler, wmsStaffHandler, wmsPartnerHandler, wmsItemHandler, wmsMetaHandler, wmsUnitHandler, wmsSkuHandler, wmsStockHandler, wmsStockLogHandler, wmsTransferHandler, wmsTransferSkuHandler)
	jobJob := job.NewJob(transaction, logger, sidSid)
	userJob := job.NewUserJob(jobJob, sysUserRepository)
	jobServer := server.NewJobServer(logger, userJob)
	appApp := newApp(httpServer, jobServer)
	return appApp, func() {
	}, nil
}

// wire.go:

var repositorySet = wire.NewSet(repository.NewDB, repository.NewRedis, repository.NewRepository, repository.NewTransaction, repository.NewSysConfigRepository, repository.NewSysDictRepository, repository.NewSysMenuRepository, repository.NewSysApiRepository, repository.NewSysRoleRepository, repository.NewSysUserRepository, repository.NewSysTenantRepository, repository.NewSysLogRepository, repository.NewCmsMetaRepository, repository.NewCmsPostRepository, repository.NewWmsAreaRepository, repository.NewWmsPartnerRepository, repository.NewWmsItemRepository, repository.NewWmsMetaRepository, repository.NewWmsUnitRepository, repository.NewWmsSkuRepository, repository.NewWmsStockRepository, repository.NewWmsStockLogRepository, repository.NewWmsTransferRepository, repository.NewWmsTransferSkuRepository)

var serviceSet = wire.NewSet(service.NewService, service.NewAuthService, service.NewUploadService, service.NewSysConfigService, service.NewSysDictService, service.NewSysMenuService, service.NewSysApiService, service.NewSysRoleService, service.NewSysUserService, service.NewSysTenantService, service.NewSysLogService, service.NewCmsMetaService, service.NewCmsPostService, service.NewWmsAreaService, service.NewWmsStaffService, service.NewWmsPartnerService, service.NewWmsItemService, service.NewWmsMetaService, service.NewWmsUnitService, service.NewWmsSkuService, service.NewWmsStockService, service.NewWmsStockLogService, service.NewWmsTransferService, service.NewWmsTransferSkuService)

var handlerSet = wire.NewSet(handler.NewHandler, handler.NewAuthHandler, handler.NewUploadHandler, handler.NewSysConfigHandler, handler.NewSysDictHandler, handler.NewSysMenuHandler, handler.NewSysApiHandler, handler.NewSysRoleHandler, handler.NewSysUserHandler, handler.NewSysTenantHandler, handler.NewSysLogHandler, handler.NewCmsMetaHandler, handler.NewCmsPostHandler, handler.NewWmsAreaHandler, handler.NewWmsStaffHandler, handler.NewWmsPartnerHandler, handler.NewWmsItemHandler, handler.NewWmsMetaHandler, handler.NewWmsUnitHandler, handler.NewWmsSkuHandler, handler.NewWmsStockHandler, handler.NewWmsStockLogHandler, handler.NewWmsTransferHandler, handler.NewWmsTransferSkuHandler)

var jobSet = wire.NewSet(job.NewJob, job.NewUserJob)

var serverSet = wire.NewSet(server.NewHTTPServer, server.NewJobServer)

// build App
func newApp(
	httpServer *http.Server,
	jobServer *server.JobServer,

) *app.App {
	return app.NewApp(app.WithServer(httpServer, jobServer), app.WithName("daisy-server"))
}
