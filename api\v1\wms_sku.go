package v1

import "gorm.io/datatypes"

type WmsSkuCreateParams struct {
	Name      string         `json:"name" binding:"required,max=255" example:"iPhone 15 128GB 黑色"`
	Code      string         `json:"code" binding:"required,max=64" example:"IP15-128-BLK"`
	Summary   string         `json:"summary" example:"苹果手机 iPhone 15 128GB 黑色版本"`
	Attrs     datatypes.JSON `json:"attrs" swaggertype:"object" example:"{\"color\":\"黑色\",\"storage\":\"128GB\",\"network\":\"5G\"}"`
	Min       uint           `json:"min" example:"10"`
	Max       uint           `json:"max" example:"1000"`
	Order     int            `json:"order" example:"1"`
	Status    bool           `json:"status" example:"true"`
	ItemId    uint           `json:"itemId" binding:"required" example:"1"`
	UnitId    uint           `json:"unitId" binding:"required" example:"1"`
	TenantId  uint           `json:"tenantId" example:"1"`
	CreatedBy string         `json:"createdBy" example:"管理员"`
	UpdatedBy string         `json:"updatedBy" example:"管理员"`
}

type WmsSkuUpdateParams struct {
	WmsSkuCreateParams
}

type WmsSkuResponse struct {
	ID        uint           `json:"id"`
	Name      string         `json:"name"`
	Code      string         `json:"code"`
	Summary   string         `json:"summary"`
	Attrs     datatypes.JSON `json:"attrs"`
	Min       uint           `json:"min"`
	Max       uint           `json:"max"`
	Order     int            `json:"order"`
	Status    bool           `json:"status"`
	ItemId    uint           `json:"itemId"`
	UnitId    uint           `json:"unitId"`
	TenantId  uint           `json:"tenantId"`
	CreatedBy string         `json:"createdBy"`
	UpdatedBy string         `json:"updatedBy"`
	CreatedAt string         `json:"createdAt"`
	UpdatedAt string         `json:"updatedAt"`
}
