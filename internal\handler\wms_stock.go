package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type WmsStockHandler struct {
	*Handler
	wmsStockService service.WmsStockService
}

func NewWmsStockHandler(
	handler *Handler,
	wmsStockService service.WmsStockService,
) *WmsStockHandler {
	return &WmsStockHandler{
		Handler:         handler,
		wmsStockService: wmsStockService,
	}
}

// Create godoc
// @Summary 创建库存记录
// @Schemes
// @Description 创建新的库存记录，如果相同SKU和库位已存在则累加数量
// @Tags 仓储模块,库存管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.WmsStockCreateParams true "库存信息"
// @Success 200 {object} v1.Response
// @Router /wms/stocks [post]
func (h *WmsStockHandler) Create(ctx *gin.Context) {
	var req v1.WmsStockCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsStockService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新库存记录
// @Schemes
// @Description 更新指定ID的库存记录信息
// @Tags 仓储模块,库存管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "库存记录ID"
// @Param request body v1.WmsStockUpdateParams true "库存信息"
// @Success 200 {object} v1.Response
// @Router /wms/stocks/{id} [patch]
func (h *WmsStockHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.WmsStockUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsStockService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除库存记录
// @Schemes
// @Description 删除指定ID的库存记录
// @Tags 仓储模块,库存管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "库存记录ID"
// @Success 200 {object} v1.Response
// @Router /wms/stocks/{id} [delete]
func (h *WmsStockHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsStockService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除库存记录
// @Schemes
// @Description 批量删除指定IDs的库存记录
// @Tags 仓储模块,库存管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "库存记录IDs"
// @Success 200 {object} v1.Response
// @Router /wms/stocks [delete]
func (h *WmsStockHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsStockService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取库存记录
// @Schemes
// @Description 获取指定ID的库存记录信息
// @Tags 仓储模块,库存管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "库存记录ID"
// @Success 200 {object} v1.Response{data=v1.WmsStockResponse}
// @Router /wms/stocks/{id} [get]
func (h *WmsStockHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	stock, err := h.wmsStockService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, stock)
}

// List godoc
// @Summary 获取库存记录列表
// @Schemes
// @Description 分页获取库存记录列表
// @Tags 仓储模块,库存管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param skuId query int false "物料规格ID筛选" example:"1"
// @Param numMin query int false "最小库存数量筛选" example:"10"
// @Param numMax query int false "最大库存数量筛选" example:"1000"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.WmsStockResponse}}
// @Router /wms/stocks [get]
func (h *WmsStockHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 物料规格ID筛选
	if skuId := ctx.DefaultQuery("skuId", ""); skuId != "" {
		params.AddFilter("sku_id", skuId)
	}

	// 库存数量范围筛选
	if numMin := ctx.DefaultQuery("numMin", ""); numMin != "" {
		params.AddFilter("num_gte", numMin)
	}
	if numMax := ctx.DefaultQuery("numMax", ""); numMax != "" {
		params.AddFilter("num_lte", numMax)
	}

	// 库位路径筛选（支持包含查询）
	if areaPath := ctx.DefaultQuery("areaPath", ""); areaPath != "" {
		params.AddFilter("area_path", areaPath)
	}

	result, err := h.wmsStockService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
