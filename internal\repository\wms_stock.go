package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"github.com/lib/pq"
	"gorm.io/gorm"
)

type WmsStockRepository interface {
	Create(ctx context.Context, stock *model.WmsStock) error
	Update(ctx context.Context, stock *model.WmsStock) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.WmsStock, error)
	GetByIds(ctx context.Context, ids []uint) ([]*model.WmsStock, error)
	GetBySkuAndArea(ctx context.Context, skuId uint, areaPath pq.Int64Array) (*model.WmsStock, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.WmsStock, int64, error)
}

func NewWmsStockRepository(
	repository *Repository,
) WmsStockRepository {
	return &wmsStockRepository{
		Repository: repository,
	}
}

type wmsStockRepository struct {
	*Repository
}

func (r *wmsStockRepository) Create(ctx context.Context, stock *model.WmsStock) error {
	if err := r.DB(ctx).Create(stock).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsStockRepository) Update(ctx context.Context, stock *model.WmsStock) error {
	if err := r.DB(ctx).Save(stock).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsStockRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.WmsStock{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsStockRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.WmsStock{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsStockRepository) Get(ctx context.Context, id uint) (*model.WmsStock, error) {
	var stock model.WmsStock
	if err := r.DB(ctx).First(&stock, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &stock, nil
}

func (r *wmsStockRepository) GetByIds(ctx context.Context, ids []uint) ([]*model.WmsStock, error) {
	var stocks []*model.WmsStock
	if len(ids) == 0 {
		return stocks, nil
	}

	if err := r.DB(ctx).Where("id IN ?", ids).Find(&stocks).Error; err != nil {
		return nil, err
	}
	return stocks, nil
}

func (r *wmsStockRepository) GetBySkuAndArea(ctx context.Context, skuId uint, areaPath pq.Int64Array) (*model.WmsStock, error) {
	var stock model.WmsStock
	if err := r.DB(ctx).Where("sku_id = ? AND area_path = ?", skuId, areaPath).First(&stock).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &stock, nil
}

func (r *wmsStockRepository) List(ctx context.Context, params *pagination.Params) ([]*model.WmsStock, int64, error) {
	var records []*model.WmsStock
	var total int64

	db := r.DB(ctx).Model(&model.WmsStock{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.WmsStock{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
