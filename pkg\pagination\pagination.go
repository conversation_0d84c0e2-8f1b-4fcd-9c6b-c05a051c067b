package pagination

import (
	"fmt"
	"reflect"
	"strings"
	"sync"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// Params 定义了通用的查询参数
type Params struct {
	// 分页参数
	Page  int `form:"_page" json:"_page"`   // 当前页码
	Limit int `form:"_limit" json:"_limit"` // 每页数量

	// 排序参数
	Sort  string `form:"_sort" json:"_sort"`   // 排序字段，多个字段用逗号分隔
	Order string `form:"_order" json:"_order"` // 排序方式，asc或desc，多个方式用逗号分隔

	// 筛选参数
	Query   string                 `form:"q" json:"q"` // 全文搜索关键词
	Filters map[string]interface{} `form:"-" json:"-"` // 其他筛选条件
}

// Result 定义了分页结果
type Result struct {
	Records interface{} `json:"records"` // 数据列表
	Total   int64       `json:"total"`   // 总记录数
}

// Apply 将分页、排序和筛选条件应用到GORM查询中
func (p *Params) Apply(db *gorm.DB, model interface{}) *gorm.DB {
	// 处理筛选条件
	db = p.applyFilters(db, model)

	// 处理全文搜索
	db = p.applyFullTextSearch(db, model)

	// 处理排序
	db = p.applySort(db, model)

	// 处理分页
	db = p.applyPagination(db)

	return db
}

// modelSchema 缓存模型的结构信息
type modelSchema struct {
	AllColumns    map[string]bool
	StringColumns []string
}

var modelSchemaCache = &sync.Map{}

// parseModelSchema 使用反射解析模型的列信息，并利用缓存提高性能
func parseModelSchema(t reflect.Type) (*modelSchema, error) {
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	if t.Kind() != reflect.Struct {
		return nil, fmt.Errorf("model must be a struct, but got %v", t.Kind())
	}

	if cached, ok := modelSchemaCache.Load(t); ok {
		return cached.(*modelSchema), nil
	}

	schema := &modelSchema{
		AllColumns:    make(map[string]bool),
		StringColumns: make([]string, 0),
	}

	// 添加gorm.Model中的标准字段
	schema.AllColumns["id"] = true
	schema.AllColumns["created_at"] = true
	schema.AllColumns["updated_at"] = true
	schema.AllColumns["deleted_at"] = true

	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)

		// 处理嵌入的gorm.Model
		if field.Anonymous && field.Name == "Model" {
			// gorm.Model已经在上面添加了，跳过
			continue
		}

		gormTag := field.Tag.Get("gorm")

		if gormTag == "-" || strings.Contains(gormTag, "->") { // 忽略显式忽略的字段和只写字段
			continue
		}

		columnName := ""
		for _, tag := range strings.Split(gormTag, ";") {
			if strings.HasPrefix(tag, "column:") {
				columnName = strings.TrimPrefix(tag, "column:")
				break
			}
		}

		if columnName == "" {
			columnName = toSnakeCase(field.Name)
		}

		schema.AllColumns[columnName] = true
		if field.Type.Kind() == reflect.String {
			schema.StringColumns = append(schema.StringColumns, columnName)
		}
	}

	modelSchemaCache.Store(t, schema)
	return schema, nil
}

// applyFilters 应用筛选条件，并校验字段安全性
func (p *Params) applyFilters(db *gorm.DB, model interface{}) *gorm.DB {
	if len(p.Filters) == 0 || model == nil {
		return db
	}

	schema, err := parseModelSchema(reflect.TypeOf(model))
	if err != nil {
		return db // or log error
	}

	operatorMapping := map[string]string{
		"_gte":  ">=",
		"_lte":  "<=",
		"_ne":   "<>",
		"_like": "LIKE",
		"_any":  "ANY",
	}

	for key, value := range p.Filters {
		foundOp := false
		for suffix, op := range operatorMapping {
			if strings.HasSuffix(key, suffix) {
				field := strings.TrimSuffix(key, suffix)
				if _, ok := schema.AllColumns[field]; ok {
					if suffix == "_like" {
						db = db.Where(fmt.Sprintf("%s %s ?", field, op), fmt.Sprintf("%%%v%%", value))
					} else if suffix == "_any" {
						db = db.Where(fmt.Sprintf("? = ANY(%s)", field), value)
					} else {
						db = db.Where(fmt.Sprintf("%s %s ?", field, op), value)
					}
				}
				foundOp = true
				break
			}
		}

		if !foundOp {
			if _, ok := schema.AllColumns[key]; ok {
				db = db.Where(fmt.Sprintf("%s = ?", key), value)
			}
		}
	}
	return db
}

// applyFullTextSearch 应用全文搜索，利用缓存提高性能
func (p *Params) applyFullTextSearch(db *gorm.DB, model interface{}) *gorm.DB {
	if p.Query == "" || model == nil {
		return db
	}

	schema, err := parseModelSchema(reflect.TypeOf(model))
	if err != nil {
		return db // or log error
	}

	var conditions []string
	var values []interface{}

	for _, columnName := range schema.StringColumns {
		conditions = append(conditions, fmt.Sprintf("%s LIKE ?", columnName))
		values = append(values, fmt.Sprintf("%%%s%%", p.Query))
	}

	if len(conditions) > 0 {
		return db.Where(strings.Join(conditions, " OR "), values...)
	}

	return db
}

// applySort 应用排序，并校验字段安全性
func (p *Params) applySort(db *gorm.DB, model interface{}) *gorm.DB {
	if p.Sort == "" || model == nil {
		return db
	}

	schema, err := parseModelSchema(reflect.TypeOf(model))
	if err != nil {
		return db // or log error
	}
	allowedColumns := schema.AllColumns

	sortFields := strings.Split(p.Sort, ",")
	orderTypes := strings.Split(p.Order, ",")

	// 确保orderTypes长度与sortFields一致
	for len(orderTypes) < len(sortFields) {
		orderTypes = append(orderTypes, "asc") // 默认升序
	}

	for i, field := range sortFields {
		field = strings.TrimSpace(field)
		if field == "" {
			continue
		}

		// 新增：将小驼峰转为下划线
		field = toSnakeCase(field)

		// 安全性：只允许对模型中存在的字段进行排序
		if _, ok := allowedColumns[field]; !ok {
			continue
		}

		orderType := "asc"
		if i < len(orderTypes) {
			orderType = strings.TrimSpace(strings.ToLower(orderTypes[i]))
			if orderType != "asc" && orderType != "desc" {
				orderType = "asc" // 默认升序
			}
		}

		db = db.Order(clause.OrderByColumn{
			Column: clause.Column{Name: field},
			Desc:   orderType == "desc",
		})
	}

	return db
}

// applyPagination 应用分页
func (p *Params) applyPagination(db *gorm.DB) *gorm.DB {
	// 使用page和limit进行分页
	if p.Page > 0 && p.Limit > 0 {
		offset := (p.Page - 1) * p.Limit
		return db.Offset(offset).Limit(p.Limit)
	}

	// 如果只设置了limit，则从0开始
	if p.Limit > 0 {
		return db.Limit(p.Limit)
	}

	return db
}

// AddFilter 添加筛选条件
func (p *Params) AddFilter(key string, value interface{}) {
	if p.Filters == nil {
		p.Filters = make(map[string]interface{})
	}
	p.Filters[key] = value
}

// SetDefaults 设置默认值
func (p *Params) SetDefaults() {
	if p.Page <= 0 {
		p.Page = 1
	}
	if p.Limit <= 0 {
		p.Limit = 10
	}
}

// toSnakeCase 将驼峰命名转换为蛇形命名
func toSnakeCase(s string) string {
	var result strings.Builder
	for i, c := range s {
		if i > 0 && 'A' <= c && c <= 'Z' {
			result.WriteRune('_')
		}
		result.WriteRune(c)
	}
	return strings.ToLower(result.String())
}

// GetResult 获取分页结果
func (p *Params) GetResult(db *gorm.DB, dest interface{}, model interface{}) (int64, error) {
	// 设置默认值
	p.SetDefaults()

	query := db.Model(model)
	query = p.applyFilters(query, model)
	query = p.applyFullTextSearch(query, model)

	// 获取总记录数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return 0, err
	}

	// 应用排序和分页
	query = p.applySort(query, model)
	query = p.applyPagination(query)

	if err := query.Find(dest).Error; err != nil {
		return 0, err
	}

	return total, nil
}
