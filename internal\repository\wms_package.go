package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type WmsPackageRepository interface {
	Create(ctx context.Context, pkg *model.WmsPackage) error
	Update(ctx context.Context, pkg *model.WmsPackage) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.WmsPackage, error)
	GetByIds(ctx context.Context, ids []uint) ([]*model.WmsPackage, error)
	GetByCode(ctx context.Context, code string) (*model.WmsPackage, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.WmsPackage, int64, error)
	GetPackageSummaryByDeliverSku(ctx context.Context, tenantId uint) ([]*v1.WmsPackageSummaryResponse, error)
	BatchCreate(ctx context.Context, packages []*model.WmsPackage) error
	GeneratePackageCodes(ctx context.Context, deliverSkuId uint, count int, tenantId uint, createdBy string) ([]string, error)
}

func NewWmsPackageRepository(
	repository *Repository,
) WmsPackageRepository {
	return &wmsPackageRepository{
		Repository: repository,
	}
}

type wmsPackageRepository struct {
	*Repository
}

func (r *wmsPackageRepository) Create(ctx context.Context, pkg *model.WmsPackage) error {
	if err := r.DB(ctx).Create(pkg).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsPackageRepository) Update(ctx context.Context, pkg *model.WmsPackage) error {
	if err := r.DB(ctx).Save(pkg).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsPackageRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.WmsPackage{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsPackageRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.WmsPackage{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsPackageRepository) Get(ctx context.Context, id uint) (*model.WmsPackage, error) {
	var pkg model.WmsPackage
	if err := r.DB(ctx).First(&pkg, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &pkg, nil
}

func (r *wmsPackageRepository) GetByIds(ctx context.Context, ids []uint) ([]*model.WmsPackage, error) {
	var packages []*model.WmsPackage
	if len(ids) == 0 {
		return packages, nil
	}
	
	if err := r.DB(ctx).Where("id IN ?", ids).Find(&packages).Error; err != nil {
		return nil, err
	}
	return packages, nil
}

func (r *wmsPackageRepository) GetByCode(ctx context.Context, code string) (*model.WmsPackage, error) {
	var pkg model.WmsPackage
	if err := r.DB(ctx).Where("code = ?", code).First(&pkg).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &pkg, nil
}

func (r *wmsPackageRepository) List(ctx context.Context, params *pagination.Params) ([]*model.WmsPackage, int64, error) {
	var records []*model.WmsPackage
	var total int64

	db := r.DB(ctx).Model(&model.WmsPackage{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.WmsPackage{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

func (r *wmsPackageRepository) GetPackageSummaryByDeliverSku(ctx context.Context, tenantId uint) ([]*v1.WmsPackageSummaryResponse, error) {
	var results []*v1.WmsPackageSummaryResponse
	
	sql := `
		SELECT 
			deliver_sku_id,
			COUNT(*) as package_count,
			MAX(code) as latest_code
		FROM wms_package 
		WHERE tenant_id = ? AND deleted_at IS NULL
		GROUP BY deliver_sku_id
		ORDER BY deliver_sku_id
	`
	
	if err := r.DB(ctx).Raw(sql, tenantId).Scan(&results).Error; err != nil {
		return nil, err
	}
	
	return results, nil
}

func (r *wmsPackageRepository) BatchCreate(ctx context.Context, packages []*model.WmsPackage) error {
	if len(packages) == 0 {
		return nil
	}
	
	// 使用批量插入提高性能
	if err := r.DB(ctx).CreateInBatches(packages, 100).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsPackageRepository) GeneratePackageCodes(ctx context.Context, deliverSkuId uint, count int, tenantId uint, createdBy string) ([]string, error) {
	var codes []string
	var packages []*model.WmsPackage
	
	// 生成包装编号
	now := time.Now()
	datePrefix := now.Format("20060102")
	
	for i := 0; i < count; i++ {
		// 生成唯一编号：PKG + 日期 + 出库SKU ID + 序号
		code := fmt.Sprintf("PKG%s%04d%04d", datePrefix, deliverSkuId, i+1)
		
		// 检查编号是否已存在，如果存在则添加时间戳
		var existingCount int64
		r.DB(ctx).Model(&model.WmsPackage{}).Where("code = ?", code).Count(&existingCount)
		if existingCount > 0 {
			code = fmt.Sprintf("PKG%s%04d%04d%03d", datePrefix, deliverSkuId, i+1, now.Nanosecond()/1000000)
		}
		
		codes = append(codes, code)
		packages = append(packages, &model.WmsPackage{
			Code:         code,
			DeliverSkuId: deliverSkuId,
			TenantId:     tenantId,
			CreatedBy:    createdBy,
			UpdatedBy:    createdBy,
		})
	}
	
	// 批量创建包装记录
	if err := r.BatchCreate(ctx, packages); err != nil {
		return nil, err
	}
	
	return codes, nil
}
