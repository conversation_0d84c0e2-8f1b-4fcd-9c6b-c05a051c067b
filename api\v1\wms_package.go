package v1

type WmsPackageCreateParams struct {
	Code         string `json:"code" binding:"required,max=64" example:"PKG202412250001"`
	DeliverSkuId uint   `json:"deliverSkuId" binding:"required" example:"1"`
	TenantId     uint   `json:"tenantId" example:"1"`
	CreatedBy    string `json:"createdBy" example:"管理员"`
	UpdatedBy    string `json:"updatedBy" example:"管理员"`
}

type WmsPackageUpdateParams struct {
	Code         string `json:"code" binding:"required,max=64" example:"PKG202412250001"`
	DeliverSkuId uint   `json:"deliverSkuId" binding:"required" example:"1"`
	UpdatedBy    string `json:"updatedBy" example:"管理员"`
}

type WmsPackageResponse struct {
	ID           uint   `json:"id"`
	Code         string `json:"code"`
	DeliverSkuId uint   `json:"deliverSkuId"`
	TenantId     uint   `json:"tenantId"`
	CreatedBy    string `json:"createdBy"`
	UpdatedBy    string `json:"updatedBy"`
	CreatedAt    string `json:"createdAt"`
	UpdatedAt    string `json:"updatedAt"`
}

// 包装统计响应
type WmsPackageSummaryResponse struct {
	DeliverSkuId uint `json:"deliverSkuId"`
	PackageCount int  `json:"packageCount"`
	LatestCode   string `json:"latestCode"`
}

// 包装批次查询参数
type WmsPackageBatchParams struct {
	DeliverSkuId uint `json:"deliverSkuId" binding:"required" example:"1"`
	Count        int  `json:"count" binding:"required,min=1,max=100" example:"10"`
}

// 包装批次响应
type WmsPackageBatchResponse struct {
	Codes []string `json:"codes"`
	Count int      `json:"count"`
}
