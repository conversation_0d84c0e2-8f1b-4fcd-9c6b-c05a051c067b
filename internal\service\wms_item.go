package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/jinzhu/copier"
)

type WmsItemService interface {
	Create(ctx context.Context, req *v1.WmsItemCreateParams) error
	Update(ctx context.Context, id uint, req *v1.WmsItemUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.WmsItemResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewWmsItemService(
	service *Service,
	wmsItemRepository repository.WmsItemRepository,
) WmsItemService {
	return &wmsItemService{
		Service:           service,
		wmsItemRepository: wmsItemRepository,
	}
}

type wmsItemService struct {
	*Service
	wmsItemRepository repository.WmsItemRepository
}

// 物料相关方法实现
func (s *wmsItemService) Create(ctx context.Context, req *v1.WmsItemCreateParams) error {
	item := &model.WmsItem{}
	if err := copier.Copy(item, req); err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 租户ID
	item.TenantId = user.TenantId

	// 创建人
	if user.Nickname != "" {
		item.CreatedBy = user.Nickname
	} else {
		item.CreatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsItemRepository.Create(ctx, item); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsItemService) Update(ctx context.Context, id uint, req *v1.WmsItemUpdateParams) error {
	item, err := s.wmsItemRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if item.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	if err := copier.Copy(item, req); err != nil {
		return err
	}

	// 更新人
	if user.Nickname != "" {
		item.UpdatedBy = user.Nickname
	} else {
		item.UpdatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsItemRepository.Update(ctx, item); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsItemService) Delete(ctx context.Context, id uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 是否是该租户数据
	item, err := s.wmsItemRepository.Get(ctx, id)
	if err != nil {
		return err
	}
	if item.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsItemRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsItemService) BatchDelete(ctx context.Context, ids []uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 获取目标记录
	items, err := s.wmsItemRepository.GetByIds(ctx, ids)
	if err != nil {
		return err
	}
	
	// 过滤非本租户数据
	var newIds []uint
	for _, item := range items {
		if item.TenantId == user.TenantId {
			newIds = append(newIds, item.ID)
		}
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsItemRepository.BatchDelete(ctx, newIds); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsItemService) Get(ctx context.Context, id uint) (*v1.WmsItemResponse, error) {
	item, err := s.wmsItemRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if item.TenantId != user.TenantId {
		return nil, v1.ErrForbidden
	}

	response := &v1.WmsItemResponse{}
	if err := copier.Copy(response, item); err != nil {
		return nil, err
	}

	response.CreatedAt = item.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = item.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *wmsItemService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	params.AddFilter("tenant_id", user.TenantId)

	items, total, err := s.wmsItemRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.WmsItemResponse, 0, len(items))
	for _, item := range items {
		response := &v1.WmsItemResponse{}
		if err := copier.Copy(response, item); err != nil {
			return nil, err
		}

		response.CreatedAt = item.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = item.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
