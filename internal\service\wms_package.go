package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/jinzhu/copier"
)

type WmsPackageService interface {
	Create(ctx context.Context, req *v1.WmsPackageCreateParams) error
	Update(ctx context.Context, id uint, req *v1.WmsPackageUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.WmsPackageResponse, error)
	GetByCode(ctx context.Context, code string) (*v1.WmsPackageResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
	GetPackageSummary(ctx context.Context) ([]*v1.WmsPackageSummaryResponse, error)
	GenerateBatch(ctx context.Context, req *v1.WmsPackageBatchParams) (*v1.WmsPackageBatchResponse, error)
}

func NewWmsPackageService(
	service *Service,
	wmsPackageRepository repository.WmsPackageRepository,
) WmsPackageService {
	return &wmsPackageService{
		Service:              service,
		wmsPackageRepository: wmsPackageRepository,
	}
}

type wmsPackageService struct {
	*Service
	wmsPackageRepository repository.WmsPackageRepository
}

// 包装相关方法实现
func (s *wmsPackageService) Create(ctx context.Context, req *v1.WmsPackageCreateParams) error {
	pkg := &model.WmsPackage{}
	if err := copier.Copy(pkg, req); err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 租户ID
	pkg.TenantId = user.TenantId

	// 创建人
	if user.Nickname != "" {
		pkg.CreatedBy = user.Nickname
	} else {
		pkg.CreatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsPackageRepository.Create(ctx, pkg); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsPackageService) Update(ctx context.Context, id uint, req *v1.WmsPackageUpdateParams) error {
	pkg, err := s.wmsPackageRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if pkg.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	if err := copier.Copy(pkg, req); err != nil {
		return err
	}

	// 更新人
	if user.Nickname != "" {
		pkg.UpdatedBy = user.Nickname
	} else {
		pkg.UpdatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsPackageRepository.Update(ctx, pkg); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsPackageService) Delete(ctx context.Context, id uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 是否是该租户数据
	pkg, err := s.wmsPackageRepository.Get(ctx, id)
	if err != nil {
		return err
	}
	if pkg.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsPackageRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsPackageService) BatchDelete(ctx context.Context, ids []uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 获取目标记录
	packages, err := s.wmsPackageRepository.GetByIds(ctx, ids)
	if err != nil {
		return err
	}

	// 过滤非本租户数据
	var newIds []uint
	for _, pkg := range packages {
		if pkg.TenantId == user.TenantId {
			newIds = append(newIds, pkg.ID)
		}
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsPackageRepository.BatchDelete(ctx, newIds); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsPackageService) Get(ctx context.Context, id uint) (*v1.WmsPackageResponse, error) {
	pkg, err := s.wmsPackageRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if pkg.TenantId != user.TenantId {
		return nil, v1.ErrForbidden
	}

	response := &v1.WmsPackageResponse{}
	if err := copier.Copy(response, pkg); err != nil {
		return nil, err
	}

	response.CreatedAt = pkg.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = pkg.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *wmsPackageService) GetByCode(ctx context.Context, code string) (*v1.WmsPackageResponse, error) {
	pkg, err := s.wmsPackageRepository.GetByCode(ctx, code)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if pkg.TenantId != user.TenantId {
		return nil, v1.ErrForbidden
	}

	response := &v1.WmsPackageResponse{}
	if err := copier.Copy(response, pkg); err != nil {
		return nil, err
	}

	response.CreatedAt = pkg.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = pkg.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *wmsPackageService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	params.AddFilter("tenant_id", user.TenantId)

	packages, total, err := s.wmsPackageRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.WmsPackageResponse, 0, len(packages))
	for _, pkg := range packages {
		response := &v1.WmsPackageResponse{}
		if err := copier.Copy(response, pkg); err != nil {
			return nil, err
		}

		response.CreatedAt = pkg.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = pkg.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}

func (s *wmsPackageService) GetPackageSummary(ctx context.Context) ([]*v1.WmsPackageSummaryResponse, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	return s.wmsPackageRepository.GetPackageSummaryByDeliverSku(ctx, user.TenantId)
}

func (s *wmsPackageService) GenerateBatch(ctx context.Context, req *v1.WmsPackageBatchParams) (*v1.WmsPackageBatchResponse, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 确定创建人
	createdBy := user.Username
	if user.Nickname != "" {
		createdBy = user.Nickname
	}

	codes, err := s.wmsPackageRepository.GeneratePackageCodes(ctx, req.DeliverSkuId, req.Count, user.TenantId, createdBy)
	if err != nil {
		return nil, err
	}

	return &v1.WmsPackageBatchResponse{
		Codes: codes,
		Count: len(codes),
	}, nil
}
