package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/jinzhu/copier"
)

type WmsTransferSkuService interface {
	Create(ctx context.Context, req *v1.WmsTransferSkuCreateParams) error
	Update(ctx context.Context, id uint, req *v1.WmsTransferSkuUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.WmsTransferSkuResponse, error)
	GetByTransferId(ctx context.Context, transferId uint) ([]*v1.WmsTransferSkuResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
	BatchCreate(ctx context.Context, reqs []*v1.WmsTransferSkuCreateParams) error
}

func NewWmsTransferSkuService(
	service *Service,
	wmsTransferSkuRepository repository.WmsTransferSkuRepository,
) WmsTransferSkuService {
	return &wmsTransferSkuService{
		Service:                  service,
		wmsTransferSkuRepository: wmsTransferSkuRepository,
	}
}

type wmsTransferSkuService struct {
	*Service
	wmsTransferSkuRepository repository.WmsTransferSkuRepository
}

// 调拨单物料相关方法实现
func (s *wmsTransferSkuService) Create(ctx context.Context, req *v1.WmsTransferSkuCreateParams) error {
	transferSku := &model.WmsTransferSku{}
	if err := copier.Copy(transferSku, req); err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 租户ID
	transferSku.TenantId = user.TenantId

	// 创建人
	if user.Nickname != "" {
		transferSku.CreatedBy = user.Nickname
	} else {
		transferSku.CreatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsTransferSkuRepository.Create(ctx, transferSku); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsTransferSkuService) Update(ctx context.Context, id uint, req *v1.WmsTransferSkuUpdateParams) error {
	transferSku, err := s.wmsTransferSkuRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if transferSku.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	if err := copier.Copy(transferSku, req); err != nil {
		return err
	}

	// 更新人
	if user.Nickname != "" {
		transferSku.UpdatedBy = user.Nickname
	} else {
		transferSku.UpdatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsTransferSkuRepository.Update(ctx, transferSku); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsTransferSkuService) Delete(ctx context.Context, id uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 是否是该租户数据
	transferSku, err := s.wmsTransferSkuRepository.Get(ctx, id)
	if err != nil {
		return err
	}
	if transferSku.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsTransferSkuRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsTransferSkuService) BatchDelete(ctx context.Context, ids []uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 获取目标记录
	transferSkus, err := s.wmsTransferSkuRepository.GetByIds(ctx, ids)
	if err != nil {
		return err
	}

	// 过滤非本租户数据
	var newIds []uint
	for _, transferSku := range transferSkus {
		if transferSku.TenantId == user.TenantId {
			newIds = append(newIds, transferSku.ID)
		}
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsTransferSkuRepository.BatchDelete(ctx, newIds); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsTransferSkuService) Get(ctx context.Context, id uint) (*v1.WmsTransferSkuResponse, error) {
	transferSku, err := s.wmsTransferSkuRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if transferSku.TenantId != user.TenantId {
		return nil, v1.ErrForbidden
	}

	response := &v1.WmsTransferSkuResponse{}
	if err := copier.Copy(response, transferSku); err != nil {
		return nil, err
	}

	response.CreatedAt = transferSku.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = transferSku.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *wmsTransferSkuService) GetByTransferId(ctx context.Context, transferId uint) ([]*v1.WmsTransferSkuResponse, error) {
	transferSkus, err := s.wmsTransferSkuRepository.GetByTransferId(ctx, transferId)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据并转换格式
	var responses []*v1.WmsTransferSkuResponse
	for _, transferSku := range transferSkus {
		if transferSku.TenantId == user.TenantId {
			response := &v1.WmsTransferSkuResponse{}
			if err := copier.Copy(response, transferSku); err != nil {
				return nil, err
			}

			response.CreatedAt = transferSku.CreatedAt.Format(time.RFC3339)
			response.UpdatedAt = transferSku.UpdatedAt.Format(time.RFC3339)

			responses = append(responses, response)
		}
	}

	return responses, nil
}

func (s *wmsTransferSkuService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	params.AddFilter("tenant_id", user.TenantId)

	transferSkus, total, err := s.wmsTransferSkuRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.WmsTransferSkuResponse, 0, len(transferSkus))
	for _, transferSku := range transferSkus {
		response := &v1.WmsTransferSkuResponse{}
		if err := copier.Copy(response, transferSku); err != nil {
			return nil, err
		}

		response.CreatedAt = transferSku.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = transferSku.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}

func (s *wmsTransferSkuService) BatchCreate(ctx context.Context, reqs []*v1.WmsTransferSkuCreateParams) error {
	if len(reqs) == 0 {
		return nil
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 转换为模型对象
	transferSkus := make([]*model.WmsTransferSku, 0, len(reqs))
	for _, req := range reqs {
		transferSku := &model.WmsTransferSku{}
		if err := copier.Copy(transferSku, req); err != nil {
			return err
		}

		// 设置租户ID和创建人
		transferSku.TenantId = user.TenantId
		if user.Nickname != "" {
			transferSku.CreatedBy = user.Nickname
		} else {
			transferSku.CreatedBy = user.Username
		}

		transferSkus = append(transferSkus, transferSku)
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		return s.wmsTransferSkuRepository.BatchCreate(ctx, transferSkus)
	})
}
