package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type WmsTransferHandler struct {
	*Handler
	wmsTransferService service.WmsTransferService
}

func NewWmsTransferHandler(
	handler *Handler,
	wmsTransferService service.WmsTransferService,
) *WmsTransferHandler {
	return &WmsTransferHandler{
		Handler:            handler,
		wmsTransferService: wmsTransferService,
	}
}

// Create godoc
// @Summary 创建调拨单
// @Schemes
// @Description 创建新的调拨单记录
// @Tags 仓储模块,调拨管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.WmsTransferCreateParams true "调拨单信息"
// @Success 200 {object} v1.Response
// @Router /wms/transfers [post]
func (h *WmsTransferHandler) Create(ctx *gin.Context) {
	var req v1.WmsTransferCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsTransferService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新调拨单
// @Schemes
// @Description 更新指定ID的调拨单信息
// @Tags 仓储模块,调拨管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "调拨单ID"
// @Param request body v1.WmsTransferUpdateParams true "调拨单信息"
// @Success 200 {object} v1.Response
// @Router /wms/transfers/{id} [patch]
func (h *WmsTransferHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.WmsTransferUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsTransferService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除调拨单
// @Schemes
// @Description 删除指定ID的调拨单
// @Tags 仓储模块,调拨管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "调拨单ID"
// @Success 200 {object} v1.Response
// @Router /wms/transfers/{id} [delete]
func (h *WmsTransferHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsTransferService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除调拨单
// @Schemes
// @Description 批量删除指定IDs的调拨单
// @Tags 仓储模块,调拨管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "调拨单IDs"
// @Success 200 {object} v1.Response
// @Router /wms/transfers [delete]
func (h *WmsTransferHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsTransferService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取调拨单
// @Schemes
// @Description 获取指定ID的调拨单信息
// @Tags 仓储模块,调拨管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "调拨单ID"
// @Success 200 {object} v1.Response{data=v1.WmsTransferResponse}
// @Router /wms/transfers/{id} [get]
func (h *WmsTransferHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	transfer, err := h.wmsTransferService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, transfer)
}

// GetByCode godoc
// @Summary 根据编号获取调拨单
// @Schemes
// @Description 根据调拨单编号获取调拨单信息
// @Tags 仓储模块,调拨管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param code path string true "调拨单编号"
// @Success 200 {object} v1.Response{data=v1.WmsTransferResponse}
// @Router /wms/transfers/code/{code} [get]
func (h *WmsTransferHandler) GetByCode(ctx *gin.Context) {
	code := ctx.Param("code")
	if code == "" {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	transfer, err := h.wmsTransferService.GetByCode(ctx, code)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, transfer)
}

// List godoc
// @Summary 获取调拨单列表
// @Schemes
// @Description 分页获取调拨单列表
// @Tags 仓储模块,调拨管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param code query string false "调拨单编号筛选" example:"TRF"
// @Param type query int false "调拨单类型筛选" example:"1"
// @Param status query int false "调拨单状态筛选" example:"1"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.WmsTransferResponse}}
// @Router /wms/transfers [get]
func (h *WmsTransferHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 调拨单编号筛选
	if code := ctx.DefaultQuery("code", ""); code != "" {
		params.AddFilter("code_like", code)
	}

	// 调拨单类型筛选
	if transferType := ctx.DefaultQuery("type", ""); transferType != "" {
		params.AddFilter("type", transferType)
	}

	// 调拨单状态筛选
	if status := ctx.DefaultQuery("status", ""); status != "" {
		params.AddFilter("status", status)
	}

	// 描述筛选
	if summary := ctx.DefaultQuery("summary", ""); summary != "" {
		params.AddFilter("summary_like", summary)
	}

	result, err := h.wmsTransferService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
