package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type WmsPackageHandler struct {
	*Handler
	wmsPackageService service.WmsPackageService
}

func NewWmsPackageHandler(
	handler *Handler,
	wmsPackageService service.WmsPackageService,
) *WmsPackageHandler {
	return &WmsPackageHandler{
		Handler:           handler,
		wmsPackageService: wmsPackageService,
	}
}

// Create godoc
// @Summary 创建包装记录
// @Schemes
// @Description 创建新的包装记录
// @Tags 仓储模块,包装管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.WmsPackageCreateParams true "包装信息"
// @Success 200 {object} v1.Response
// @Router /wms/packages [post]
func (h *WmsPackageHandler) Create(ctx *gin.Context) {
	var req v1.WmsPackageCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsPackageService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新包装记录
// @Schemes
// @Description 更新指定ID的包装记录信息
// @Tags 仓储模块,包装管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "包装记录ID"
// @Param request body v1.WmsPackageUpdateParams true "包装信息"
// @Success 200 {object} v1.Response
// @Router /wms/packages/{id} [patch]
func (h *WmsPackageHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.WmsPackageUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsPackageService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除包装记录
// @Schemes
// @Description 删除指定ID的包装记录
// @Tags 仓储模块,包装管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "包装记录ID"
// @Success 200 {object} v1.Response
// @Router /wms/packages/{id} [delete]
func (h *WmsPackageHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsPackageService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除包装记录
// @Schemes
// @Description 批量删除指定IDs的包装记录
// @Tags 仓储模块,包装管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "包装记录IDs"
// @Success 200 {object} v1.Response
// @Router /wms/packages [delete]
func (h *WmsPackageHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsPackageService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取包装记录
// @Schemes
// @Description 获取指定ID的包装记录信息
// @Tags 仓储模块,包装管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "包装记录ID"
// @Success 200 {object} v1.Response{data=v1.WmsPackageResponse}
// @Router /wms/packages/{id} [get]
func (h *WmsPackageHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	pkg, err := h.wmsPackageService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, pkg)
}

// GetByCode godoc
// @Summary 根据编号获取包装记录
// @Schemes
// @Description 根据包装编号获取包装记录信息
// @Tags 仓储模块,包装管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param code path string true "包装编号"
// @Success 200 {object} v1.Response{data=v1.WmsPackageResponse}
// @Router /wms/packages/code/{code} [get]
func (h *WmsPackageHandler) GetByCode(ctx *gin.Context) {
	code := ctx.Param("code")
	if code == "" {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	pkg, err := h.wmsPackageService.GetByCode(ctx, code)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, pkg)
}

// List godoc
// @Summary 获取包装记录列表
// @Schemes
// @Description 分页获取包装记录列表
// @Tags 仓储模块,包装管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param deliverSkuId query int false "出库物料规格ID筛选" example:"1"
// @Param code query string false "包装编号筛选" example:"PKG"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.WmsPackageResponse}}
// @Router /wms/packages [get]
func (h *WmsPackageHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	// 出库物料规格ID筛选
	if deliverSkuId := ctx.DefaultQuery("deliverSkuId", ""); deliverSkuId != "" {
		params.AddFilter("deliver_sku_id", deliverSkuId)
	}

	// 包装编号筛选
	if code := ctx.DefaultQuery("code", ""); code != "" {
		params.AddFilter("code_like", code)
	}

	result, err := h.wmsPackageService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}

// GetPackageSummary godoc
// @Summary 获取包装统计
// @Schemes
// @Description 按出库SKU统计包装数量
// @Tags 仓储模块,包装管理
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} v1.Response{data=[]v1.WmsPackageSummaryResponse}
// @Router /wms/packages/summary [get]
func (h *WmsPackageHandler) GetPackageSummary(ctx *gin.Context) {
	summary, err := h.wmsPackageService.GetPackageSummary(ctx)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, summary)
}

// GenerateBatch godoc
// @Summary 批量生成包装编号
// @Schemes
// @Description 为指定出库SKU批量生成包装编号和记录
// @Tags 仓储模块,包装管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.WmsPackageBatchParams true "批量生成参数"
// @Success 200 {object} v1.Response{data=v1.WmsPackageBatchResponse}
// @Router /wms/packages/batch [post]
func (h *WmsPackageHandler) GenerateBatch(ctx *gin.Context) {
	var req v1.WmsPackageBatchParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	result, err := h.wmsPackageService.GenerateBatch(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
