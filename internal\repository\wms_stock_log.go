package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type WmsStockLogRepository interface {
	Create(ctx context.Context, log *model.WmsStockLog) error
	Update(ctx context.Context, log *model.WmsStockLog) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.WmsStockLog, error)
	GetByIds(ctx context.Context, ids []uint) ([]*model.WmsStockLog, error)
	GetByRelatedNo(ctx context.Context, relatedNo string) ([]*model.WmsStockLog, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.WmsStockLog, int64, error)
	BatchCreate(ctx context.Context, logs []*model.WmsStockLog) error
}

func NewWmsStockLogRepository(
	repository *Repository,
) WmsStockLogRepository {
	return &wmsStockLogRepository{
		Repository: repository,
	}
}

type wmsStockLogRepository struct {
	*Repository
}

func (r *wmsStockLogRepository) Create(ctx context.Context, log *model.WmsStockLog) error {
	if err := r.DB(ctx).Create(log).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsStockLogRepository) Update(ctx context.Context, log *model.WmsStockLog) error {
	if err := r.DB(ctx).Save(log).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsStockLogRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.WmsStockLog{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsStockLogRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.WmsStockLog{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *wmsStockLogRepository) Get(ctx context.Context, id uint) (*model.WmsStockLog, error) {
	var log model.WmsStockLog
	if err := r.DB(ctx).First(&log, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &log, nil
}

func (r *wmsStockLogRepository) GetByIds(ctx context.Context, ids []uint) ([]*model.WmsStockLog, error) {
	var logs []*model.WmsStockLog
	if len(ids) == 0 {
		return logs, nil
	}

	if err := r.DB(ctx).Where("id IN ?", ids).Find(&logs).Error; err != nil {
		return nil, err
	}
	return logs, nil
}

func (r *wmsStockLogRepository) GetByRelatedNo(ctx context.Context, relatedNo string) ([]*model.WmsStockLog, error) {
	var logs []*model.WmsStockLog
	if err := r.DB(ctx).Where("related_no = ?", relatedNo).Find(&logs).Error; err != nil {
		return nil, err
	}
	return logs, nil
}

func (r *wmsStockLogRepository) List(ctx context.Context, params *pagination.Params) ([]*model.WmsStockLog, int64, error) {
	var records []*model.WmsStockLog
	var total int64

	db := r.DB(ctx).Model(&model.WmsStockLog{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.WmsStockLog{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

func (r *wmsStockLogRepository) BatchCreate(ctx context.Context, logs []*model.WmsStockLog) error {
	if len(logs) == 0 {
		return nil
	}

	// 使用批量插入提高性能
	if err := r.DB(ctx).CreateInBatches(logs, 100).Error; err != nil {
		return err
	}
	return nil
}
