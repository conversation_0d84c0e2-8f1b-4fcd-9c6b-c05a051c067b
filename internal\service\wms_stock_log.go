package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/jinzhu/copier"
)

type WmsStockLogService interface {
	Create(ctx context.Context, req *v1.WmsStockLogCreateParams) error
	Update(ctx context.Context, id uint, req *v1.WmsStockLogUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.WmsStockLogResponse, error)
	GetByRelatedNo(ctx context.Context, relatedNo string) ([]*v1.WmsStockLogResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewWmsStockLogService(
	service *Service,
	wmsStockLogRepository repository.WmsStockLogRepository,
) WmsStockLogService {
	return &wmsStockLogService{
		Service:               service,
		wmsStockLogRepository: wmsStockLogRepository,
	}
}

type wmsStockLogService struct {
	*Service
	wmsStockLogRepository repository.WmsStockLogRepository
}

// 库存日志相关方法实现
func (s *wmsStockLogService) Create(ctx context.Context, req *v1.WmsStockLogCreateParams) error {
	log := &model.WmsStockLog{}
	if err := copier.Copy(log, req); err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 租户ID
	log.TenantId = user.TenantId

	// 创建人
	if user.Nickname != "" {
		log.CreatedBy = user.Nickname
	} else {
		log.CreatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsStockLogRepository.Create(ctx, log); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsStockLogService) Update(ctx context.Context, id uint, req *v1.WmsStockLogUpdateParams) error {
	log, err := s.wmsStockLogRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if log.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	if err := copier.Copy(log, req); err != nil {
		return err
	}

	// 更新人
	if user.Nickname != "" {
		log.UpdatedBy = user.Nickname
	} else {
		log.UpdatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsStockLogRepository.Update(ctx, log); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsStockLogService) Delete(ctx context.Context, id uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 是否是该租户数据
	log, err := s.wmsStockLogRepository.Get(ctx, id)
	if err != nil {
		return err
	}
	if log.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsStockLogRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsStockLogService) BatchDelete(ctx context.Context, ids []uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 获取目标记录
	logs, err := s.wmsStockLogRepository.GetByIds(ctx, ids)
	if err != nil {
		return err
	}

	// 过滤非本租户数据
	var newIds []uint
	for _, log := range logs {
		if log.TenantId == user.TenantId {
			newIds = append(newIds, log.ID)
		}
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsStockLogRepository.BatchDelete(ctx, newIds); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsStockLogService) Get(ctx context.Context, id uint) (*v1.WmsStockLogResponse, error) {
	log, err := s.wmsStockLogRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if log.TenantId != user.TenantId {
		return nil, v1.ErrForbidden
	}

	response := &v1.WmsStockLogResponse{}
	if err := copier.Copy(response, log); err != nil {
		return nil, err
	}

	response.CreatedAt = log.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = log.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *wmsStockLogService) GetByRelatedNo(ctx context.Context, relatedNo string) ([]*v1.WmsStockLogResponse, error) {
	logs, err := s.wmsStockLogRepository.GetByRelatedNo(ctx, relatedNo)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据并转换格式
	var responses []*v1.WmsStockLogResponse
	for _, log := range logs {
		if log.TenantId == user.TenantId {
			response := &v1.WmsStockLogResponse{}
			if err := copier.Copy(response, log); err != nil {
				return nil, err
			}

			response.CreatedAt = log.CreatedAt.Format(time.RFC3339)
			response.UpdatedAt = log.UpdatedAt.Format(time.RFC3339)

			responses = append(responses, response)
		}
	}

	return responses, nil
}

func (s *wmsStockLogService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	params.AddFilter("tenant_id", user.TenantId)

	logs, total, err := s.wmsStockLogRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.WmsStockLogResponse, 0, len(logs))
	for _, log := range logs {
		response := &v1.WmsStockLogResponse{}
		if err := copier.Copy(response, log); err != nil {
			return nil, err
		}

		response.CreatedAt = log.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = log.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
