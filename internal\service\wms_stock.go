package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/jinzhu/copier"
)

type WmsStockService interface {
	Create(ctx context.Context, req *v1.WmsStockCreateParams) error
	Update(ctx context.Context, id uint, req *v1.WmsStockUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.WmsStockResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewWmsStockService(
	service *Service,
	wmsStockRepository repository.WmsStockRepository,
) WmsStockService {
	return &wmsStockService{
		Service:            service,
		wmsStockRepository: wmsStockRepository,
	}
}

type wmsStockService struct {
	*Service
	wmsStockRepository repository.WmsStockRepository
}

// 库存相关方法实现
func (s *wmsStockService) Create(ctx context.Context, req *v1.WmsStockCreateParams) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 检查是否已存在相同SKU和库位的库存记录
	existingStock, err := s.wmsStockRepository.GetBySkuAndArea(ctx, req.SkuId, req.AreaPath)
	if err != nil && err != v1.ErrNotFound {
		return err
	}

	if existingStock != nil {
		// 如果已存在，则累加库存数量
		existingStock.Num += req.Num
		if user.Nickname != "" {
			existingStock.UpdatedBy = user.Nickname
		} else {
			existingStock.UpdatedBy = user.Username
		}

		return s.tm.Transaction(ctx, func(ctx context.Context) error {
			return s.wmsStockRepository.Update(ctx, existingStock)
		})
	}

	// 创建新的库存记录
	stock := &model.WmsStock{}
	if err := copier.Copy(stock, req); err != nil {
		return err
	}

	// 租户ID
	stock.TenantId = user.TenantId

	// 创建人
	if user.Nickname != "" {
		stock.CreatedBy = user.Nickname
	} else {
		stock.CreatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsStockRepository.Create(ctx, stock); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsStockService) Update(ctx context.Context, id uint, req *v1.WmsStockUpdateParams) error {
	stock, err := s.wmsStockRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if stock.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	if err := copier.Copy(stock, req); err != nil {
		return err
	}

	// 更新人
	if user.Nickname != "" {
		stock.UpdatedBy = user.Nickname
	} else {
		stock.UpdatedBy = user.Username
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsStockRepository.Update(ctx, stock); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsStockService) Delete(ctx context.Context, id uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 是否是该租户数据
	stock, err := s.wmsStockRepository.Get(ctx, id)
	if err != nil {
		return err
	}
	if stock.TenantId != user.TenantId {
		return v1.ErrForbidden
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsStockRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsStockService) BatchDelete(ctx context.Context, ids []uint) error {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 获取目标记录
	stocks, err := s.wmsStockRepository.GetByIds(ctx, ids)
	if err != nil {
		return err
	}

	// 过滤非本租户数据
	var newIds []uint
	for _, stock := range stocks {
		if stock.TenantId == user.TenantId {
			newIds = append(newIds, stock.ID)
		}
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsStockRepository.BatchDelete(ctx, newIds); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsStockService) Get(ctx context.Context, id uint) (*v1.WmsStockResponse, error) {
	stock, err := s.wmsStockRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	if stock.TenantId != user.TenantId {
		return nil, v1.ErrForbidden
	}

	response := &v1.WmsStockResponse{}
	if err := copier.Copy(response, stock); err != nil {
		return nil, err
	}

	response.CreatedAt = stock.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = stock.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *wmsStockService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 获取当前用户信息
	user := ctx.Value("user").(*model.SysUser)

	// 过滤非本租户数据
	params.AddFilter("tenant_id", user.TenantId)

	stocks, total, err := s.wmsStockRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.WmsStockResponse, 0, len(stocks))
	for _, stock := range stocks {
		response := &v1.WmsStockResponse{}
		if err := copier.Copy(response, stock); err != nil {
			return nil, err
		}

		response.CreatedAt = stock.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = stock.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
